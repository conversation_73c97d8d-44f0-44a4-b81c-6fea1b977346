import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Animated, {
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import * as Haptics from 'expo-haptics';

import { Colors } from '../constants/Colors';
import { CircularProgress } from './CircularProgress';
import LottieIcon from './LottieIcon';
import HealthService, { HealthData, HeartRateReading, StepsData } from '../services/HealthService';

const { width } = Dimensions.get('window');

interface HealthMonitorProps {
  style?: any;
}

export const HealthMonitor: React.FC<HealthMonitorProps> = ({ style }) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [heartRateData, setHeartRateData] = useState<HeartRateReading | null>(null);
  const [stepsData, setStepsData] = useState<StepsData | null>(null);
  const [bodyTemperature, setBodyTemperature] = useState<{ temperature: number; timestamp: number; unit: string } | null>(null);
  const [oxygenSaturation, setOxygenSaturation] = useState<{ spO2: number; timestamp: number; unit: string } | null>(null);

  // Animation values
  const heartBeatScale = useSharedValue(1);
  const pulseOpacity = useSharedValue(0.3);

  useEffect(() => {
    initializeHealth();
    return () => {
      HealthService.stopMonitoring();
    };
  }, []);

  useEffect(() => {
    if (isMonitoring) {
      // Start heart beat animation
      heartBeatScale.value = withRepeat(
        withTiming(1.2, { duration: 600 }),
        -1,
        true
      );
      
      // Start pulse animation
      pulseOpacity.value = withRepeat(
        withTiming(0.8, { duration: 1000 }),
        -1,
        true
      );

      // Update data every 2 seconds
      const interval = setInterval(updateHealthData, 2000);
      return () => clearInterval(interval);
    } else {
      heartBeatScale.value = withSpring(1);
      pulseOpacity.value = withSpring(0.3);
    }
  }, [isMonitoring]);

  const initializeHealth = async () => {
    try {
      console.log('🔄 Initializing real health sensor monitoring...');
      const initialized = await HealthService.initializeHealthMonitoring();
      if (initialized) {
        await HealthService.startMonitoring();
        setIsMonitoring(true);
        await updateHealthData(); // Make sure to await the async update
        console.log('✅ Health monitoring initialized with real sensors');
      }
    } catch (error) {
      console.error('❌ Error initializing health monitoring:', error);
    }
  };

  const updateHealthData = async () => {
    try {
      const health = (HealthService as any).getCurrentHealthData();
      const steps = (HealthService as any).getCurrentSteps();

      setHealthData(health);
      setStepsData(steps);

      // Fetch all health metrics asynchronously with real sensor data
      const [heartRate, temperature, oxygen] = await Promise.all([
        (HealthService as any).getCurrentHeartRate(),
        (HealthService as any).getBodyTemperature(),
        (HealthService as any).getOxygenSaturation()
      ]);

      setHeartRateData(heartRate);
      setBodyTemperature(temperature);
      setOxygenSaturation(oxygen);

      console.log('✅ Updated health data with real sensor readings:', {
        heartRate: heartRate.bpm,
        temperature: temperature.temperature,
        oxygen: oxygen.spO2,
        steps: steps.steps
      });

    } catch (error) {
      console.error('❌ Error fetching health metrics:', error);
    }
  };

  const toggleMonitoring = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      if (isMonitoring) {
        await HealthService.stopMonitoring();
        setIsMonitoring(false);
      } else {
        await HealthService.startMonitoring();
        setIsMonitoring(true);
        await updateHealthData(); // Make sure to await the async update
      }
    } catch (error) {
      console.error('Error toggling monitoring:', error);
    }
  };

  const heartBeatAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartBeatScale.value }],
  }));

  const pulseAnimatedStyle = useAnimatedStyle(() => ({
    opacity: pulseOpacity.value,
  }));

  const getHeartRateColor = (bpm: number) => {
    if (bpm < 60) return '#3B82F6'; // Blue for low
    if (bpm > 100) return '#EF4444'; // Red for high
    return '#10B981'; // Green for normal
  };

  const getStepsProgress = (steps: number) => {
    const dailyGoal = 10000;
    return Math.min((steps / dailyGoal) * 100, 100);
  };

  if (!healthData || !heartRateData || !stepsData) {
    return (
      <Animated.View entering={FadeInUp.duration(600)} style={[styles.container, style]}>
        <View style={styles.loadingCard}>
          <LottieIcon name="heartbeat" size={32} color={Colors.brand} enableHaptics={false} />
          <Text style={styles.loadingText}>Initializing Health Monitor...</Text>
        </View>
      </Animated.View>
    );
  }

  return (
    <Animated.View entering={FadeInUp.duration(600)} style={[styles.container, style]}>
      {/* Modern Health Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Health Metrics</Text>
          <Text style={styles.subtitle}>Live monitoring</Text>
        </View>

        <View style={[styles.statusIndicator, isMonitoring && styles.statusActive]}>
          <View style={[styles.statusDot, isMonitoring && styles.statusDotActive]} />
          <Text style={[styles.statusText, isMonitoring && styles.statusTextActive]}>
            {isMonitoring ? 'Active' : 'Inactive'}
          </Text>
        </View>
      </View>

      {/* FIRST ROW - Heart Rate & Steps - SEPARATE CONTAINER */}
      <View style={styles.healthRowContainer}>
        {/* Heart Rate Card */}
        <View style={styles.healthMetricItem}>
          <View style={styles.cardHeader}>
            <View style={styles.iconWrapper}>
              <LottieIcon
                name="heartbeat"
                size={18}
                color={Colors.brand}
                enableHaptics={false}
              />
            </View>
            <Text style={styles.metricLabel}>Heart Rate</Text>
          </View>

          <View style={styles.metricContent}>
            <View style={styles.valueContainer}>
              <Text style={[styles.metricValue, { color: getHeartRateColor(heartRateData.bpm) }]}>
                {heartRateData.bpm}
              </Text>
              <Text style={styles.metricUnit}>BPM</Text>
            </View>

            <View style={styles.statusContainer}>
              <View style={styles.confidenceBar}>
                <View
                  style={[
                    styles.confidenceFill,
                    {
                      width: `${heartRateData.confidence * 100}%`,
                      backgroundColor: getHeartRateColor(heartRateData.bpm)
                    }
                  ]}
                />
              </View>
              <Text style={styles.statusLabel}>
                {heartRateData.bpm < 60 ? 'Low' : heartRateData.bpm > 100 ? 'High' : 'Normal'}
              </Text>
            </View>
          </View>
        </View>

        {/* Steps Card */}
        <View style={styles.healthMetricItem}>
          <View style={styles.cardHeader}>
            <View style={styles.iconWrapper}>
              <LottieIcon
                name="progressCircle"
                size={18}
                color={Colors.brand}
                enableHaptics={false}
              />
            </View>
            <Text style={styles.metricLabel}>Daily Steps</Text>
          </View>

          <View style={styles.metricContent}>
            <View style={styles.valueContainer}>
              <Text style={[styles.metricValue, { color: Colors.brand }]}>
                {stepsData.steps.toLocaleString()}
              </Text>
              <Text style={styles.metricUnit}>steps</Text>
            </View>

            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${getStepsProgress(stepsData.steps)}%`,
                      backgroundColor: Colors.brand
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressLabel}>
                {Math.round(getStepsProgress(stepsData.steps))}% of 10K goal
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* SECOND ROW - Body Temperature & Oxygen Saturation - SEPARATE CONTAINER */}
      {(bodyTemperature || oxygenSaturation) && (
        <View style={styles.healthRowContainer}>
          {/* Body Temperature Card */}
          {bodyTemperature && (
            <View style={styles.healthMetricItem}>
            <View style={styles.cardHeader}>
              <View style={styles.iconWrapper}>
                <LottieIcon
                  name="heartbeat"
                  size={18}
                  color="#FF6B6B"
                  enableHaptics={false}
                />
              </View>
              <Text style={styles.metricLabel}>Body Temp</Text>
            </View>

            <View style={styles.metricContent}>
              <View style={styles.valueContainer}>
                <Text style={[styles.metricValue, { color: '#FF6B6B' }]}>
                  {bodyTemperature.temperature}
                </Text>
                <Text style={styles.metricUnit}>{bodyTemperature.unit}</Text>
              </View>

              <View style={styles.progressContainer}>
                <Text style={styles.statusLabel}>
                  {bodyTemperature.temperature >= 36.1 && bodyTemperature.temperature <= 37.2 ? 'Normal' :
                   bodyTemperature.temperature < 36.1 ? 'Low' : 'High'}
                </Text>
              </View>
            </View>
          </View>
        )}

          {/* Oxygen Saturation Card */}
          {oxygenSaturation && (
            <View style={styles.healthMetricItem}>
            <View style={styles.cardHeader}>
              <View style={styles.iconWrapper}>
                <LottieIcon
                  name="heartbeat"
                  size={18}
                  color="#4ECDC4"
                  enableHaptics={false}
                />
              </View>
              <Text style={styles.metricLabel}>O₂ Saturation</Text>
            </View>

            <View style={styles.metricContent}>
              <View style={styles.valueContainer}>
                <Text style={[styles.metricValue, { color: '#4ECDC4' }]}>
                  {oxygenSaturation.spO2}
                </Text>
                <Text style={styles.metricUnit}>{oxygenSaturation.unit}</Text>
              </View>

              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${oxygenSaturation.spO2}%`,
                        backgroundColor: '#4ECDC4'
                      }
                    ]}
                  />
                </View>
                <Text style={styles.statusLabel}>
                  {oxygenSaturation.spO2 >= 95 ? 'Normal' : 'Low'}
                </Text>
              </View>
            </View>
            </View>
          )}
        </View>
      )}

      {/* Summary Stats */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <View style={styles.summaryIconWrapper}>
            <LottieIcon name="fire" size={14} color={Colors.brandForeground} enableHaptics={false} />
          </View>
          <View style={styles.summaryContent}>
            <Text style={styles.summaryValue}>{stepsData.calories}</Text>
            <Text style={styles.summaryLabel}>Calories</Text>
          </View>
        </View>

        <View style={styles.divider} />

        <View style={styles.summaryItem}>
          <View style={styles.summaryIconWrapper}>
            <LottieIcon name="scan" size={14} color={Colors.brandForeground} enableHaptics={false} />
          </View>
          <View style={styles.summaryContent}>
            <Text style={styles.summaryValue}>{(stepsData.distance / 1000).toFixed(1)}</Text>
            <Text style={styles.summaryLabel}>Kilometers</Text>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  loadingCard: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.4,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 2,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: Colors.muted,
  },
  statusActive: {
    backgroundColor: Colors.brandMuted,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.mutedForeground,
    marginRight: 6,
  },
  statusDotActive: {
    backgroundColor: Colors.brand,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.mutedForeground,
  },
  statusTextActive: {
    color: Colors.brand,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  metricCard: {
    width: '48%', // Force exactly 2 per row
    backgroundColor: Colors.card,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
    padding: 16,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },

  // NEW HEALTH ROW STYLES - FORCE 2 PER ROW
  healthRowContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  healthMetricItem: {
    flex: 1, // EXACTLY 50% each - FORCES 2 PER ROW
    backgroundColor: Colors.card,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
    padding: 16,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconWrapper: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  metricLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.foreground,
  },
  metricContent: {
    marginTop: 4,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12,
  },
  metricValue: {
    fontSize: 28,
    fontWeight: '700',
    letterSpacing: -0.5,
    marginRight: 4,
  },
  metricUnit: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  statusContainer: {
    marginTop: 4,
  },
  confidenceBar: {
    width: '100%',
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: 2,
    marginBottom: 6,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 2,
  },
  statusLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.mutedForeground,
    textAlign: 'right',
  },
  progressContainer: {
    marginTop: 4,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: 2,
    marginBottom: 6,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.mutedForeground,
    textAlign: 'right',
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: Colors.brand,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginTop: 8,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  summaryIconWrapper: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  summaryContent: {
    justifyContent: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.brandForeground,
    marginBottom: 2,
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  divider: {
    width: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 16,
  },
});

export default HealthMonitor;
