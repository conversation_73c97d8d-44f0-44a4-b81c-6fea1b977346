import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import DatabaseIntegrationService from './DatabaseIntegrationService';

export interface WeekPlan {
  week: Array<{
    day: string;
    meals: { [key: string]: string };
  }>;
  weekNumber: number;
  year: number;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  isActive: boolean;
  generatedAt: number; // timestamp
}

export interface WeeklyPlanConfig {
  autoGenerate: boolean;
  generateDaysAhead: number; // How many days before week ends to generate next week
  maxStoredWeeks: number; // Maximum number of weeks to keep in storage
}

class WeeklyPlanManager {
  private static instance: WeeklyPlanManager;
  private readonly WEEKLY_PLANS_KEY = 'weekly_plans_storage';
  private readonly CURRENT_WEEK_KEY = 'current_week_info';
  private readonly CONFIG_KEY = 'weekly_plan_config';

  static getInstance(): WeeklyPlanManager {
    if (!WeeklyPlanManager.instance) {
      WeeklyPlanManager.instance = new WeeklyPlanManager();
    }
    return WeeklyPlanManager.instance;
  }

  // Get current week information
  getCurrentWeekInfo(): { weekNumber: number; year: number; startDate: Date; endDate: Date } {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const pastDaysOfYear = (now.getTime() - startOfYear.getTime()) / 86400000;
    const weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
    
    // Calculate start of current week (Monday)
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const startDate = new Date(now);
    startDate.setDate(now.getDate() + daysToMonday);
    startDate.setHours(0, 0, 0, 0);
    
    // Calculate end of current week (Sunday)
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);
    endDate.setHours(23, 59, 59, 999);
    
    return {
      weekNumber,
      year: now.getFullYear(),
      startDate,
      endDate
    };
  }

  // Check if current stored plan is still valid for this week
  async isCurrentPlanValid(): Promise<boolean> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const storedWeekInfo = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);
      
      if (!storedWeekInfo) {
        console.log('📅 No stored week info found');
        return false;
      }
      
      const stored = JSON.parse(storedWeekInfo);
      const isValid = stored.weekNumber === currentWeekInfo.weekNumber && 
                     stored.year === currentWeekInfo.year;
      
      console.log(`📅 Week plan validity check: ${isValid ? 'VALID' : 'EXPIRED'}`);
      console.log(`📅 Current: Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log(`📅 Stored: Week ${stored.weekNumber}, ${stored.year}`);
      
      return isValid;
    } catch (error) {
      console.error('❌ Error checking plan validity:', error);
      return false;
    }
  }

  // Generate new weekly plan for current week
  async generateWeeklyPlan(userProfile: any): Promise<WeekPlan | null> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      console.log(`📅 Generating new weekly plan for Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      
      // Generate meal plan using existing API
      const mealPlanResult = await ApiService.generateWeeklyMealPlan({
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.dailyCalorieGoal || 2000,
        mealsPerDay: 3,
        preferences: userProfile.foodPreferences || []
      });
      
      if (!mealPlanResult || !mealPlanResult.week) {
        throw new Error('Failed to generate meal plan from API');
      }
      
      const weekPlan: WeekPlan = {
        week: mealPlanResult.week,
        weekNumber: currentWeekInfo.weekNumber,
        year: currentWeekInfo.year,
        startDate: currentWeekInfo.startDate.toISOString(),
        endDate: currentWeekInfo.endDate.toISOString(),
        isActive: true,
        generatedAt: Date.now()
      };
      
      // Store the new plan
      await this.storeWeeklyPlan(weekPlan);
      await this.updateCurrentWeekInfo(currentWeekInfo);
      
      console.log(`✅ Generated and stored weekly plan for Week ${currentWeekInfo.weekNumber}`);
      return weekPlan;
      
    } catch (error) {
      console.error('❌ Error generating weekly plan:', error);
      return null;
    }
  }

  // Store weekly plan in AsyncStorage
  private async storeWeeklyPlan(weekPlan: WeekPlan): Promise<void> {
    try {
      // Get existing plans
      const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];
      
      // Mark all other plans as inactive
      const updatedPlans = existingPlans.map(plan => ({ ...plan, isActive: false }));
      
      // Add new plan
      updatedPlans.push(weekPlan);
      
      // Keep only recent weeks (cleanup old plans)
      const config = await this.getConfig();
      const sortedPlans = updatedPlans.sort((a, b) => b.generatedAt - a.generatedAt);
      const plansToKeep = sortedPlans.slice(0, config.maxStoredWeeks);
      
      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plansToKeep));
      
      // Also store in database for backup (if available)
      try {
        await DatabaseIntegrationService.saveWeeklyPlanToDatabase(weekPlan);
      } catch (error) {
        console.warn('⚠️ Could not save to database, continuing with AsyncStorage only:', error);
      }
      
    } catch (error) {
      console.error('❌ Error storing weekly plan:', error);
      throw error;
    }
  }

  // Update current week info
  private async updateCurrentWeekInfo(weekInfo: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.CURRENT_WEEK_KEY, JSON.stringify(weekInfo));
    } catch (error) {
      console.error('❌ Error updating current week info:', error);
      throw error;
    }
  }

  // Get active weekly plan
  async getActiveWeeklyPlan(): Promise<WeekPlan | null> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return null;
      
      const plans: WeekPlan[] = JSON.parse(plansJson);
      const activePlan = plans.find(plan => plan.isActive);
      
      return activePlan || null;
    } catch (error) {
      console.error('❌ Error getting active weekly plan:', error);
      return null;
    }
  }

  // Get configuration
  private async getConfig(): Promise<WeeklyPlanConfig> {
    try {
      const configJson = await AsyncStorage.getItem(this.CONFIG_KEY);
      if (configJson) {
        return JSON.parse(configJson);
      }
      
      // Default configuration
      const defaultConfig: WeeklyPlanConfig = {
        autoGenerate: true,
        generateDaysAhead: 2, // Generate new plan 2 days before current week ends
        maxStoredWeeks: 8 // Keep 8 weeks of history
      };
      
      await AsyncStorage.setItem(this.CONFIG_KEY, JSON.stringify(defaultConfig));
      return defaultConfig;
    } catch (error) {
      console.error('❌ Error getting config:', error);
      return {
        autoGenerate: true,
        generateDaysAhead: 2,
        maxStoredWeeks: 8
      };
    }
  }

  // Check if we need to generate next week's plan
  async shouldGenerateNextWeek(): Promise<boolean> {
    try {
      const config = await this.getConfig();
      if (!config.autoGenerate) return false;
      
      const currentWeekInfo = this.getCurrentWeekInfo();
      const now = new Date();
      const daysUntilWeekEnd = Math.ceil((currentWeekInfo.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      console.log(`📅 Days until week end: ${daysUntilWeekEnd}`);
      console.log(`📅 Generate days ahead setting: ${config.generateDaysAhead}`);
      
      return daysUntilWeekEnd <= config.generateDaysAhead;
    } catch (error) {
      console.error('❌ Error checking if should generate next week:', error);
      return false;
    }
  }

  // Main function to ensure current week has a valid plan
  async ensureCurrentWeekPlan(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('📅 Ensuring current week has valid plan...');
      
      // Check if current plan is valid
      const isValid = await this.isCurrentPlanValid();
      
      if (isValid) {
        console.log('📅 Current plan is valid, returning existing plan');
        return await this.getActiveWeeklyPlan();
      }
      
      console.log('📅 Current plan is invalid or missing, generating new plan');
      return await this.generateWeeklyPlan(userProfile);
      
    } catch (error) {
      console.error('❌ Error ensuring current week plan:', error);
      return null;
    }
  }

  // Get plan history for analytics
  async getPlanHistory(limit: number = 4): Promise<WeekPlan[]> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return [];
      
      const plans: WeekPlan[] = JSON.parse(plansJson);
      return plans
        .sort((a, b) => b.generatedAt - a.generatedAt)
        .slice(0, limit);
    } catch (error) {
      console.error('❌ Error getting plan history:', error);
      return [];
    }
  }
}

export default WeeklyPlanManager.getInstance();
